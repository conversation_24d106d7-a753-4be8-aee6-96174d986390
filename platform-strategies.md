# Platform Content Fetching and Token Refresh Strategies

This document outlines the strategies used for fetching content and managing API tokens for each integrated platform.

## Meta (Facebook & Instagram)

### Content Fetching

- **Strategy**: Content is fetched on-demand from the Graph API. The `fetchLatestFacebookGraphMedia` and `fetchLatestInstagramWithFacebookGraphMedia` functions handle paginated requests to retrieve posts.
- **Webhooks**: The application uses webhooks for real-time updates. The `meta.ts` webhook handler processes notifications for new comments, reactions, and posts, which are then passed to a debouncer (`DEBOUNCER_DO`) to manage API calls efficiently.
- **Evaluation**: This hybrid approach is robust, combining on-demand fetching with real-time updates. The use of a debouncer is a good practice to prevent API rate-limiting issues.

### Token Refresh

- **Strategy**: Token management is centralized in `meta.ts`, with `refreshMetaTokenFactory` and `exchangeMetaTokenFactory` handling token exchanges and refreshes for all Meta platforms. A cron job in `refresh-tokens.ts` runs every six hours and refreshes tokens that are due to expire within 14 days.
- **Evaluation**: The centralized token management is efficient and reduces code duplication. The scheduled cron job ensures that tokens are refreshed proactively, preventing interruptions in service.

## TikTok

### Content Fetching

- **Strategy**: Content is fetched on-demand using the `fetchTikTokVideos` function. There is no webhook for real-time content updates.
- **Webhooks**: The `tiktok.ts` webhook handler is minimal and primarily serves to satisfy app review requirements. It logs incoming events but does not process them for content updates.
- **Evaluation**: The current strategy relies solely on on-demand fetching, which may not be ideal for real-time applications. Implementing content updates through webhooks would improve the platform's responsiveness.

### Token Refresh

- **Strategy**: There is no explicit token refresh logic in `tiktok.ts`. However, the `refresh-tokens.ts` cron job is designed to handle token refreshes for all platforms, so it is likely that TikTok tokens are also managed by this scheduled task.
- **Evaluation**: While the cron job likely handles token refreshes, the absence of explicit refresh logic in the platform file makes it less transparent. A more explicit implementation would improve clarity.

## YouTube

### Content Fetching

- **Strategy**: YouTube employs a hybrid approach. The `getYoutubePosts` function fetches content on-demand, while `youtube-pubsub.ts` sets up PubSubHubbub subscriptions for real-time notifications of new videos.
- **Webhooks**: Webhooks are used to trigger a sync process via a queue (`SYNC_QUEUE`) when a new video is published. This ensures that new content is fetched and stored promptly.
- **Evaluation**: This is a highly effective strategy that combines the benefits of on-demand fetching and real-time updates, ensuring that content is always up-to-date.

### Token Refresh

- **Strategy**: The `refreshYoutubeToken` function handles token refreshes automatically when an authentication error is detected. Additionally, the `refresh-tokens.ts` cron job proactively refreshes tokens that are nearing expiration.
- **Evaluation**: This dual approach to token management is very robust. It ensures that tokens are almost always valid, providing a seamless and reliable user experience.
