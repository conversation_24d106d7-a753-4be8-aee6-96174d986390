"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { SiFacebook } from "@icons-pack/react-simple-icons";
import { Loader2 } from "lucide-react";

interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
}

interface FacebookPageSelectorProps {
  pagesData: {
    connectionId: string;
    token: string | null;
    userAccessToken: string;
    expiresAt: string;
    pages: FacebookPage[];
  };
  projectId?: string;
  connectionId?: string;
  token?: string;
}

export function FacebookPageSelector({
  pagesData,
  projectId,
  connectionId,
  token,
}: FacebookPageSelectorProps) {
  const [selectedPageId, setSelectedPageId] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleConnect = async () => {
    if (!selectedPageId) {
      toast({
        title: "No Page Selected",
        description: "Please select a Facebook page to connect.",
        variant: "destructive",
      });
      return;
    }

    setIsConnecting(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/meta/select-page`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...pagesData,
            selectedPageId,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to connect Facebook page");
      }

      const result = await response.json();
      
      if (result.success && result.redirectUrl) {
        toast({
          title: "Success!",
          description: "Facebook page connected successfully.",
        });
        router.push(result.redirectUrl);
      } else {
        throw new Error("Unexpected response format");
      }
    } catch (error) {
      console.error("Facebook page selection error:", error);
      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to connect Facebook page. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <SiFacebook className="h-5 w-5 text-blue-600" />
          Select Facebook Page
        </CardTitle>
        <CardDescription>
          Choose which Facebook page you want to connect to your project.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup value={selectedPageId} onValueChange={setSelectedPageId}>
          {pagesData.pages.map((page) => (
            <div key={page.id} className="flex items-center space-x-2">
              <RadioGroupItem value={page.id} id={page.id} />
              <Label
                htmlFor={page.id}
                className="flex-1 cursor-pointer p-4 border rounded-lg hover:bg-muted"
              >
                <div className="font-medium">{page.name}</div>
                <div className="text-sm text-muted-foreground">
                  Page ID: {page.id}
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>

        <div className="flex gap-4">
          <Button
            onClick={handleConnect}
            disabled={!selectedPageId || isConnecting}
            className="flex-1"
          >
            {isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting...
              </>
            ) : (
              "Connect Selected Page"
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => {
              // Go back to the connection page or dashboard
              if (token) {
                router.push(`/connect/${token}`);
              } else if (projectId && connectionId) {
                router.push(`/dashboard/projects/${projectId}/connections/${connectionId}`);
              } else {
                router.push("/dashboard/projects");
              }
            }}
            disabled={isConnecting}
          >
            Cancel
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <p>
            <strong>Note:</strong> You can only connect one Facebook page at a time. 
            If you need to connect multiple pages, create separate connections.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
