"use client";

import { useState } from "react";
import { BaseOAuthConnector } from "./base-oauth-connector";
import { SiFacebook } from "@icons-pack/react-simple-icons";

interface FacebookConnectorProps {
  connectionId: string;
  token?: string;
}

const permissions = [
  {
    name: "Pages Access",
    description: "Access to your Facebook Pages that you manage",
    required: true,
  },
  {
    name: "Page Content",
    description: "Read access to posts, photos, and videos on your Pages",
    required: true,
  },
  {
    name: "Page Insights",
    description:
      "Access to Page analytics, reach, engagement, and audience data",
    required: true,
  },
  {
    name: "Post Management",
    description:
      "Ability to manage posts on your Pages (if needed for future features)",
    required: false,
  },
  {
    name: "User Engagement",
    description: "Access to likes, comments, and shares on your Page content",
    required: true,
  },
];

export function FacebookConnector({
  connectionId,
  token,
}: FacebookConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // Redirect to Meta OAuth authorize endpoint
      const authUrl = new URL(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/meta/authorize`
      );

      if (connectionId) {
        authUrl.searchParams.set("connection_id", connectionId);
      } else if (token) {
        authUrl.searchParams.set("token", token);
      } else {
        throw new Error("No connection ID or token provided");
      }

      authUrl.searchParams.set("type", "facebook_login");
      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("Facebook OAuth error:", error);
      setIsConnecting(false);
      throw error;
    }
  };

  return (
    <BaseOAuthConnector
      platform="facebook"
      platformName="Facebook"
      icon={SiFacebook}
      description="Connect your Facebook Pages to access posts, insights, and engagement data."
      permissions={permissions}
      onConnect={handleConnect}
      isConnecting={isConnecting}
      connectionId={connectionId}
      token={token}
      color="bg-blue-600"
    />
  );
}
