import { <PERSON><PERSON><PERSON><PERSON>, XCircle, AlertCircle, Calendar } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { getConnection } from "@/lib/fetch/getConnection";
import { getAllGeneratedLinks } from "@/lib/fetch/getGeneratedLinks";
import TestConnectionButton from "./_components/TestConnectionButton";
import CreateLinkButton from "./_components/CreateLinkButton";
import { DateTime } from "luxon";
import { availableNetworks } from "@/constants/availableNetworks";
import ReplaceAccountButton from "./_components/ReplaceAccountButton";
import { DisconnectButton } from "./_components/DisconnectButton";
import CopyButton from "@/components/dashboard/CopyButton";
import DeleteLinkButton from "./_components/DeleteLinkButton";
import { headers } from "next/headers";
import Link from "next/link";
import { BasicConnectionSettings } from "./_components/BasicConnectionSettings";
import { DeleteConnectionSettings } from "./_components/DeleteConnectionSettings";
import { DebugCard } from "./_components/DebugCard";

export default async function SingleConnectionPage({
  params,
}: {
  params: Promise<{ id: string; connectionId: string }>;
}) {
  const { id: currentProjectId, connectionId } = await params;
  const connection = await getConnection(currentProjectId, connectionId);
  const oauthLinks = await getAllGeneratedLinks(currentProjectId, connectionId);

  console.log("connection", connection);

  const headersList = await headers();
  const domain = headersList.get("host") || "";

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "expired":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case "inactive":
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      error: "destructive",
      expired: "secondary",
      inactive: "outline",
    } as const;

    const labels = {
      active: "Active",
      error: "Error",
      expired: "Expired",
      inactive: "Inactive",
    } as const;
    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const ConnectionPlatformIcon =
    availableNetworks.find((n) => n.slug === connection.platform)?.icon ||
    AlertCircle;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <ConnectionPlatformIcon className="h-8 w-8" />
        <h2 className="text-2xl font-semibold tracking-tight">
          {connection.name}
        </h2>
        {getStatusBadge(connection.status)}
      </div>

      {/* Connection Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Connection Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-muted-foreground">Status</Label>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(connection.status)}
                <span className="font-medium">{connection.status}</span>
              </div>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Last Sync</Label>
              <p className="font-medium mt-1">
                {connection.lastSyncedAt
                  ? DateTime.fromJSDate(
                      new Date(connection.lastSyncedAt)
                    ).toRelative()
                  : "never"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <BasicConnectionSettings
        projectId={currentProjectId}
        connection={connection}
      />

      {/* Connected Account */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Connected Account</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center space-x-4">
              <img
                src={
                  connection.platformAccountProfilePictureUrl ||
                  "/placeholder.svg"
                }
                alt="Profile picture"
                className="h-12 w-12 rounded-full"
              />
              <div className="flex-grow">
                <div className="font-medium">
                  {connection.platformAccountName ?? "Not Connected"}
                </div>
                {connection.platformAccountUsername && (
                  <div className="text-sm text-muted-foreground">
                    @{connection.platformAccountUsername}
                  </div>
                )}
                {connection.platformAccountDescription && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {connection.platformAccountDescription}
                  </p>
                )}
                {connection.status !== "auth_needed" && (
                  <div className="text-sm text-muted-foreground mt-2">
                    {connection.platformAccountFollowers} followers ·{" "}
                    {connection.platformAccountFollowing} following ·{" "}
                    {connection.platformPostCount} posts
                  </div>
                )}
              </div>
            </div>
            {connection.status !== "auth_needed" ? (
              <div className="flex items-center space-x-2">
                <TestConnectionButton
                  platform={connection.platform}
                  connectionId={connection.id}
                />
                <ReplaceAccountButton connectionId={connectionId} />
                <DisconnectButton
                  platform={connection.platform}
                  connectionId={connectionId}
                  platformAccountName={
                    connection.platformAccountName || undefined
                  }
                />
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  href={`/dashboard/projects/${currentProjectId}/connections/${connectionId}/connect`}
                >
                  <Button size="sm" variant="outline">
                    Connect Account
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Generated OAuth Links */}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Generated OAuth Links</CardTitle>
              <CardDescription>
                Manage OAuth 2.0 links for this connection.
              </CardDescription>
            </div>
            <CreateLinkButton
              projectId={currentProjectId}
              connectionId={connectionId}
            />
          </div>
        </CardHeader>
        <CardContent>
          {oauthLinks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {oauthLinks.map((link) => {
                  const isExpired =
                    link.expiresAt &&
                    DateTime.fromISO(link.expiresAt) <= DateTime.now();
                  const isActive = link.isActive && !isExpired;

                  // Determine status: active, expired (due to time), or inactive (due to connection established)
                  let status: string;
                  if (isActive) {
                    status = "active";
                  } else if (isExpired) {
                    status = "expired";
                  } else {
                    status = "inactive"; // Link was deactivated (connection established)
                  }

                  return (
                    <TableRow key={link.id}>
                      <TableCell>
                        <div className="font-medium">{link.name}</div>
                        <div className="text-xs text-muted-foreground font-mono truncate max-w-[200px]">
                          {link.token}
                        </div>
                        {status === "inactive" && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Deactivated after connection was established
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(status)}
                          {getStatusBadge(status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{link.expiresAt}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{link.createdAt}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <CopyButton
                            title={
                              isActive
                                ? "Copy Link"
                                : "Link is no longer active"
                            }
                            value={`${domain}/connect/${link.token}`}
                            disabled={!isActive}
                          />
                          <DeleteLinkButton
                            name={link.name}
                            linkId={link.id}
                            connectionId={connectionId}
                            projectId={currentProjectId}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No OAuth links generated for this connection yet.
            </p>
          )}
        </CardContent>
      </Card>
      {/*}<NotificationsConnectionSettings />{*/}
      {/* Permissions */}
      {/*}
      {Array.isArray(connection.scopes) &&
        (connection.scopes as string[]).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Permissions</CardTitle>
              <CardDescription>
                Current permissions granted for this platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {(connection.platform_connections.scopes as string[]).map(
                  (permission) => (
                    <Badge key={permission} variant="secondary">
                      {permission}
                    </Badge>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
          {*/}
      <DeleteConnectionSettings
        name={connection.name}
        projectId={currentProjectId}
        connectionId={connectionId}
      />
      <DebugCard
        projectId={currentProjectId}
        connectionId={connection.id}
        expiresAt={connection.tokenExpiresAt}
      />
    </div>
  );
}
