import { notFound } from "next/navigation";
import React from "react";
import { FacebookPageSelector } from "@/components/oauth/facebook-page-selector";

interface SelectFacebookPageProps {
  params: Promise<{ id: string; connectionId: string }>;
  searchParams: Promise<{ data?: string }>;
}

const SelectFacebookPagePage = async ({
  params,
  searchParams,
}: SelectFacebookPageProps) => {
  const { id, connectionId } = await params;
  const { data } = await searchParams;

  if (!data) {
    notFound();
  }

  let pagesData;
  try {
    pagesData = JSON.parse(decodeURIComponent(data));
  } catch (error) {
    console.error("Failed to parse pages data:", error);
    notFound();
  }

  if (!pagesData.pages || !Array.isArray(pagesData.pages)) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">Select Facebook Page</h1>
          <p className="text-muted-foreground mt-2">
            You manage multiple Facebook pages. Please select the page you want to connect.
          </p>
        </div>
        
        <FacebookPageSelector
          pagesData={pagesData}
          projectId={id}
          connectionId={connectionId}
        />
      </div>
    </div>
  );
};

export default SelectFacebookPagePage;
