import { z } from "zod";
import {
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiPagingSchema,
  InsertPost,
  PlatformConnection,
  Post,
} from "../types";
import {
  checkMetaTokenFactory,
  exchangeMetaTokenFactory,
  refreshMetaTokenFactory,
} from "./meta";
import * as constants from "../constants";
import { HTTPException } from "hono/http-exception";
import { decryptToken, encryptToken } from "../token-utils";
import { getDbClient } from "../database-service";
import { platformConnections } from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";
import { PlatformInformation } from ".";

const InstagramBusinessUserResponseSchema = z.object({
  id: z.string(),
  username: z.string(),
  name: z.string().optional(),
  biography: z.string().optional(),
  followers_count: z.number().optional(),
  follows_count: z.number().optional(),
  media_count: z.number().optional(),
  profile_picture_url: z.string().url().optional(),
});

export const checkInstagramToken = checkMetaTokenFactory("instagram");

export const refreshInstagramToken = refreshMetaTokenFactory(
  "instagram",
  "ig_refresh_token"
);

export const exchangeInstagramToken = exchangeMetaTokenFactory("instagram");

export const getPlatformInformation = async (
  connection: PlatformConnection,
  env: Bindings
): Promise<PlatformInformation> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,username,name,biography,followers_count,follows_count,profile_picture_url,media_count";
  const userResponse = await fetch(
    `https://graph.instagram.com/v19.0/me?fields=${fields}&access_token=${accessToken}`
  );

  if (!userResponse.ok) {
    const errorText = await userResponse.text();
    console.error("Failed to get user info from graph", errorText);
    throw new HTTPException(400, { message: "Failed to get user info" });
  }

  const userData = await userResponse.json();
  const userValidation =
    InstagramBusinessUserResponseSchema.safeParse(userData);

  if (!userValidation.success) {
    console.error("Invalid user response:", userValidation.error);
    throw new HTTPException(400, { message: "Invalid user response" });
  }

  const data = userValidation.data;
  return {
    id: data.id,
    name: data.name || data.username,
    username: data.username,
    description: data.biography,
    followers_count: data.followers_count,
    follows_count: data.follows_count,
    posts_count: data.media_count,
    profile_picture_url: data.profile_picture_url,
  };
};

const mapGraphApiMediaToPost = (
  media: GraphApiMedia,
  connection: PlatformConnection
): Post => {
  return {
    platformConnectionId: connection.id,
    mediaId: media.id,
    platform: "instagram",
    likeCount: media.like_count ?? 0,
    commentsCount: media.comments_count ?? 0,
    caption: media.caption ?? null,
    mediaUrl: media.media_url ?? null,
    mediaType: media.media_type ?? "UNKNOWN",
    permalink: media.permalink ?? null,
    timestamp: new Date(media.timestamp),
    lastFetched: new Date(),
    lastWebhookUpdate: null,
    engagement: {},
  };
};

export const getPost = async (
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<Post | null> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const url = `https://graph.instagram.com/${constants.META_API_VERSION}/${postId}?fields=${fields}&access_token=${accessToken}`;
  const response = await fetch(url);
  if (!response.ok) {
    return null;
  }
  const postData = await response.json();
  const postValidation = GraphApiMediaSchema.safeParse(postData);

  if (!postValidation.success) {
    return null;
  }

  return mapGraphApiMediaToPost(postValidation.data, connection);
};

export const getPosts = async (
  connection: PlatformConnection,
  { limit, cursor }: { limit?: number; cursor?: string },
  env: Bindings
): Promise<{ posts: Post[]; nextCursor?: string }> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url = `https://graph.instagram.com/${constants.META_API_VERSION}/${connection.platformAccountId}/media?fields=${fields}&limit=${limit ?? 10}&access_token=${accessToken}`;

  if (cursor) {
    url += `&after=${cursor}`;
  }

  const response = await fetch(url);
  if (!response.ok) {
    return { posts: [] };
  }
  const pageJson = await response.json();
  const pageValidation = z
    .object({
      data: z.array(GraphApiMediaSchema),
      paging: GraphApiPagingSchema.optional(),
    })
    .safeParse(pageJson);

  if (!pageValidation.success) {
    return { posts: [] };
  }

  const posts = pageValidation.data.data.map((post) =>
    mapGraphApiMediaToPost(post, connection)
  );

  return {
    posts,
    nextCursor: pageValidation.data.paging?.cursors?.after,
  };
};

export function mapInstagramBusinessPostData(
  post: GraphApiMedia,
  connection: PlatformConnection
): InsertPost {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "instagram",
    likeCount: post.like_count ?? 0,
    commentsCount: post.comments_count ?? 0,
    caption: post.caption ?? null,
    mediaUrl: post.media_url ?? null,
    mediaType: post.media_type ?? "UNKNOWN",
    permalink: post.permalink ?? null,
    timestamp: new Date(post.timestamp),
    lastFetched: new Date(),
  };
}
