// src/platforms/index.ts
import {
  Platform,
  Bindings,
  InsertPost,
  AppContext,
  PlatformConnection,
  Post,
  YouTubeVideosResponseSchema,
} from "../types";
import * as YouTube from "./youtube";
import * as Meta from "./meta";
import { decryptToken } from "../token-utils";
import { fetchWithRetry } from "../graph-api-shared";
import * as TikTok from "./tiktok";
import * as Instagram from "./instagram-business";
import * as InstaWithFacebook from "./instagram-with-facebook";
import * as Facebook from "./facebook";

export interface PlatformInformation {
  id: string;
  username?: string;
  name: string;
  description?: string;
  followers_count?: number;
  follows_count?: number;
  posts_count?: number;
  profile_picture_url?: string;
  views_count?: number;
}
export interface PlatformAdapter {
  getPost(
    connection: PlatformConnection,
    postId: string,
    env: Bindings
  ): Promise<Post | null>;
  getPosts(
    connection: PlatformConnection,
    args: { limit?: number; cursor?: string },
    env: Bindings
  ): Promise<{ posts: Post[]; nextCursor?: string }>;
  getPlatformInformation(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<PlatformInformation | null>;
  checkToken(accessToken: string, env: Bindings): Promise<boolean>;
  exchangeToken(
    shortLivedToken: string,
    env: Bindings
  ): Promise<{ accessToken: string; refreshToken?: string; expiresAt: Date }>;
  refreshToken?(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<{
    accessToken: string;
    expiresAt: Date;
  }>;
  mapPostData(post: any, connection: PlatformConnection): InsertPost;
}

class YouTubePlatformAdapter implements PlatformAdapter {
  checkToken = YouTube.checkYoutubeToken;
  refreshToken = YouTube.refreshYoutubeToken;
  exchangeToken = YouTube.exchangeYoutubeToken;
  mapPostData = YouTube.mapYoutubePostData;
  getPost = YouTube.getYoutubePost;
  getPosts = YouTube.getYoutubePosts;
  getPlatformInformation = YouTube.getYoutubePlatformInformation;
}

class InstagramBusinessPlatformAdapter implements PlatformAdapter {
  checkToken = Instagram.checkInstagramToken;
  refreshToken = Instagram.refreshInstagramToken;
  exchangeToken = Instagram.exchangeInstagramToken;
  mapPostData = Instagram.mapInstagramBusinessPostData;
  getPost = Instagram.getPost;
  getPosts = Instagram.getPosts;
  getPlatformInformation = Instagram.getPlatformInformation;
}

class InstagramWithFacebookPlatformAdapter implements PlatformAdapter {
  checkToken = InstaWithFacebook.checkInstagramWithFacebookToken;
  refreshToken = InstaWithFacebook.refreshInstagramToken;
  exchangeToken = InstaWithFacebook.exchangeInstagramWithFacebookToken;
  mapPostData = InstaWithFacebook.mapPostData;
  getPost = InstaWithFacebook.getPost;
  getPosts = InstaWithFacebook.getPosts;
  getPlatformInformation = InstaWithFacebook.getPlatformInformation;
}

class BasicDisplayPlatformAdapter implements PlatformAdapter {
  async getPost(
    connection: PlatformConnection,
    postId: string,
    env: Bindings
  ): Promise<Post | null> {
    throw new Error("Not implemented");
  }
  async getPosts(
    connection: PlatformConnection,
    args: { limit?: number; cursor?: string },
    env: Bindings
  ): Promise<{ posts: Post[]; nextCursor?: string }> {
    throw new Error("Not implemented");
  }
  async getPlatformInformation(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<PlatformInformation | null> {
    throw new Error("Not implemented");
  }
  async checkToken(accessToken: string, env: Bindings): Promise<boolean> {
    throw new Error("Basic Display API is deprecated and should not be used.");
  }
  async refreshToken(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<{ accessToken: string; expiresAt: Date }> {
    throw new Error("Basic Display API is deprecated and should not be used.");
  }
  async exchangeToken(
    shortLivedToken: string,
    env: Bindings
  ): Promise<{ accessToken: string; expiresAt: Date }> {
    throw new Error("Basic Display API is deprecated and should not be used.");
  }
  mapPostData(post: any, connection: PlatformConnection): InsertPost {
    throw new Error("Basic Display API is deprecated and should not be used.");
  }
}

class FacebookPlatformAdapter implements PlatformAdapter {
  checkToken = Facebook.checkFacebookToken;
  refreshToken = Facebook.refreshFacebookToken;
  exchangeToken = Facebook.exchangeFacebookToken;
  mapPostData = Facebook.mapFacebookPostData;
  getPost = Facebook.getPost;
  getPosts = Facebook.getPosts;
  getPlatformInformation = Facebook.getPlatformInformation;
}

/*
class TikTokPlatformAdapter implements PlatformAdapter {
  checkToken = TikTok.checkTikTokToken;
  async refreshToken(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<{ accessToken: string; expiresAt: Date } | null> {
    // TODO: Implement TikTok token refresh logic
    console.log(
      `RefreshToken for TikTok connection ${connection.id} not implemented.`
    );
    return null;
  }
  async syncPosts(
    connection: PlatformConnection,
    accessToken: string,
    env: Bindings,
    limit?: number
  ) {
    const res = await TikTok.fetchTikTokVideos(
      connection.platformAccountId!,
      accessToken,
      env,
      limit ?? connection.postLimit ?? undefined
    );
    if (res && res.error) {
      throw res.error;
    }
    return { posts: res.posts || [] };
  }
  mapPostData = TikTok.mapTikTokPost;
}
*/
// Platform Registry
const platformAdapters: Record<Platform, PlatformAdapter> = {
  youtube: new YouTubePlatformAdapter(),
  instagram_business: new InstagramBusinessPlatformAdapter(),
  instagram_with_facebook: new InstagramWithFacebookPlatformAdapter(),
  facebook: new FacebookPlatformAdapter(),
  tiktok: new BasicDisplayPlatformAdapter(),
  instagram: new BasicDisplayPlatformAdapter(),
};

export function getPlatformAdapter(platform: Platform): PlatformAdapter {
  const adapter = platformAdapters[platform.toLowerCase() as Platform];
  if (!adapter) {
    throw new Error(`Platform ${platform} not supported - 1`);
  }
  return adapter;
}

// Re-export platform-specific functions for backward compatibility
export { YouTube, Meta };
