import { z } from "zod";
import { fetchWithRetry } from "../graph-api-shared";
import {
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiPagingSchema,
  InsertPost,
  PlatformConnection,
  Post,
} from "../types";
import { checkFacebookToken } from "./facebook";
import { exchangeMetaTokenFactory, refreshMetaTokenFactory } from "./meta";
import { logErrorToAnalytics } from "../analytics-utils";
import * as constants from "../constants";
import { decryptToken } from "../token-utils";
import { HTTPException } from "hono/http-exception";

const InstagramWithFacebookUserResponseSchema = z.object({
  id: z.string(),
  username: z.string(),
  name: z.string().optional(),
  biography: z.string().optional(),
  followers_count: z.number().optional(),
  follows_count: z.number().optional(),
  media_count: z.number().optional(),
  profile_picture_url: z.string().url().optional(),
});

export const checkInstagramWithFacebookToken = checkFacebookToken;
export const refreshInstagramToken = refreshMetaTokenFactory(
  "facebook",
  "fb_refresh_token"
);
export const exchangeInstagramWithFacebookToken =
  exchangeMetaTokenFactory("facebook");

export const getPlatformInformation = async (
  connection: PlatformConnection,
  env: Bindings
): Promise<{
  id: string;
  username?: string;
  name: string;
  description?: string;
  followers_count?: number;
  follows_count?: number;
  posts_count?: number;
  profile_picture_url?: string;
}> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,username,name,biography,followers_count,follows_count,profile_picture_url,media_count";
  const userResponse = await fetch(
    `https://graph.facebook.com/v19.0/me?fields=${fields}&access_token=${accessToken}`
  );

  if (!userResponse.ok) {
    const errorText = await userResponse.text();
    console.error("Failed to get user info from graph", errorText);
    throw new HTTPException(400, { message: "Failed to get user info" });
  }

  const userData = await userResponse.json();
  const userValidation =
    InstagramWithFacebookUserResponseSchema.safeParse(userData);

  if (!userValidation.success) {
    console.error("Invalid user response:", userValidation.error);
    throw new HTTPException(400, { message: "Invalid user response" });
  }

  return {
    id: userValidation.data.id,
    name: userValidation.data.name || userValidation.data.username || "",
    username: userValidation.data.username,
    description: userValidation.data.biography,
    followers_count: userValidation.data.followers_count,
    follows_count: userValidation.data.follows_count,
    posts_count: userValidation.data.media_count,
    profile_picture_url: userValidation.data.profile_picture_url,
  };
};

export async function fetchLatestInstagramWithFacebookGraphMedia(
  accessToken: string,
  platformAccountId: string, // IG User ID oder FB Page ID
  env: Bindings,
  limit?: number
): Promise<{ posts: GraphApiMedia[]; error?: any } | null> {
  const contextInfo = `Latest Graph Media for ${platformAccountId}`;
  const collectedPosts: GraphApiMedia[] = [];
  const collectedTagPosts: GraphApiMedia[] = [];
  const effectiveLimit = limit ?? Number.MAX_SAFE_INTEGER;
  const fetchLimitPerPage = 50; // Set a constant page size
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url: string | null =
    `https://graph.instagram.com/${constants.META_API_VERSION}/${platformAccountId}/media?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;
  let tagUrl: string | null =
    `https://graph.instagram.com/${constants.META_API_VERSION}/${platformAccountId}/tags?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;

  try {
    while (url && collectedPosts.length < effectiveLimit) {
      const remainingLimit = effectiveLimit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(url);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph media page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      );
      const pageJson = await response.json();

      const pageValidation = z
        .object({
          data: z.array(GraphApiMediaSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "GRAPH_MEDIA_PAGE_VALIDATION_ERROR",
          "Invalid graph media page response",
          { errors: pageValidation.error.flatten(), received: pageJson }
        );
        break;
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedPosts.push(...pageData.data);
      } else {
        break;
      }
      if (collectedPosts.length >= effectiveLimit) break;

      url = pageData.paging?.next || null;
      if (url)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }

    // Fetch tagged posts if available
    while (tagUrl && collectedTagPosts.length < effectiveLimit) {
      const remainingLimit = effectiveLimit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(tagUrl);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph tag page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      );
      const pageJson = await response.json();

      const pageValidation = z
        .object({
          data: z.array(GraphApiMediaSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "GRAPH_MEDIA_PAGE_VALIDATION_ERROR",
          "Invalid graph media page response",
          { errors: pageValidation.error.flatten(), received: pageJson }
        );
        break;
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedTagPosts.push(...pageData.data);
      } else {
        break;
      }
      if (collectedTagPosts.length >= effectiveLimit) break;

      tagUrl = pageData.paging?.next || null;
      if (tagUrl)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }

    // Combine collected posts and tag posts sorted by timestamp
    collectedPosts.push(...collectedTagPosts);
    collectedPosts.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    console.log(
      `GRAPH_API: Fetched ${collectedPosts.length} graph posts for ${platformAccountId}.`
    );
    return { posts: collectedPosts.slice(0, effectiveLimit) };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch latest graph media for ${platformAccountId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { platformAccountId, error: String(error) }
    );
    return {
      posts: [],
      error: error,
    };
  }
}

export async function syncPosts(
  connection: PlatformConnection,
  env: Bindings,
  limit?: number
): Promise<any> {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Instagram access token");
  }

  const mediaResult = await fetchLatestInstagramWithFacebookGraphMedia(
    accessToken,
    connection.platformAccountId!,
    env,
    limit ?? connection.postLimit ?? undefined
  );

  if (!mediaResult) {
    throw new Error(`Meta API client failed`);
  }
  if (mediaResult.error) {
    throw mediaResult.error;
  }

  return { posts: mediaResult.posts || [] };
}

export function mapPostData(post: any, connection: PlatformConnection): Post {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "instagram",
    likeCount: (post as any).like_count ?? 0,
    commentsCount: (post as any).comments_count ?? 0,
    caption: post.caption,
    mediaUrl: post.media_url,
    mediaType: post.media_type,
    permalink: post.permalink,
    timestamp: post.timestamp ? new Date(post.timestamp) : new Date(),
    lastFetched: new Date(),
    lastWebhookUpdate: null,
    engagement: {},
  };
}

export const getPosts = async (
  connection: PlatformConnection,
  { limit, cursor }: { limit?: number; cursor?: string },
  env: Bindings
) => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Instagram access token");
  }

  const mediaResult = await fetchLatestInstagramWithFacebookGraphMedia(
    accessToken,
    connection.platformAccountId!,
    env,
    limit ?? connection.postLimit ?? undefined
  );

  if (!mediaResult) {
    throw new Error(`Meta API client failed`);
  }
  if (mediaResult.error) {
    throw mediaResult.error;
  }

  const posts = mediaResult.posts.map((post) => mapPostData(post, connection));

  return {
    posts: posts,
    nextCursor: undefined, // TODO: Implement cursor logic
  };
};

export const getPost = async (
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<Post | null> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Instagram access token");
  }

  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const url = `https://graph.facebook.com/${constants.META_API_VERSION}/${postId}?fields=${fields}&access_token=${accessToken}`;
  const response = await fetch(url);
  if (!response.ok) {
    return null;
  }
  const postData = await response.json();
  const postValidation = GraphApiMediaSchema.safeParse(postData);

  if (!postValidation.success) {
    return null;
  }

  return mapPostData(postValidation.data, connection);
};
