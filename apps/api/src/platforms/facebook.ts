import { z } from "zod";
import { fetchWithRetry } from "../graph-api-shared";
import {
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiPagingSchema,
} from "../types";
import {
  checkMetaTokenFactory,
  exchangeMetaTokenFactory,
  refreshMetaTokenFactory,
} from "./meta";
import { logErrorToAnalytics } from "../analytics-utils";
import * as constants from "../constants";
import { decryptToken } from "../token-utils";
import { Post, PlatformConnection, InsertPost } from "../types";
import { PlatformInformation } from ".";
import { HTTPException } from "hono/http-exception";

export const checkFacebookToken = checkMetaTokenFactory("facebook");
export const refreshFacebookToken = refreshMetaTokenFactory(
  "facebook",
  "fb_refresh_token"
);
export const exchangeFacebookToken = exchangeMetaTokenFactory("facebook");

export const getPlatformInformation = async (
  connection: PlatformConnection,
  env: Bindings
): Promise<PlatformInformation> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,name,about,fan_count,picture.type(large),followers_count,new_like_count";
  const userResponse = await fetch(
    `https://graph.facebook.com/v19.0/me?fields=${fields}&access_token=${accessToken}`
  );

  if (!userResponse.ok) {
    const errorText = await userResponse.text();
    console.error("Failed to get user info from graph", errorText);
    throw new HTTPException(400, { message: "Failed to get user info" });
  }

  const userData = await userResponse.json();
  const FacebookPageDetailsSchema = z.object({
    id: z.string(),
    name: z.string(),
    about: z.string().optional(),
    fan_count: z.number().optional(),
    followers_count: z.number().optional(),
    new_like_count: z.number().optional(),
    picture: z
      .object({
        data: z.object({
          url: z.string().url(),
        }),
      })
      .optional(),
  });
  const userValidation = FacebookPageDetailsSchema.safeParse(userData);

  if (!userValidation.success) {
    console.error("Invalid user response:", userValidation.error);
    throw new HTTPException(400, { message: "Invalid user response" });
  }

  const data = userValidation.data;
  return {
    id: data.id,
    name: data.name,
    description: data.about,
    followers_count: data.followers_count || data.fan_count,
    posts_count: undefined, // Not available directly
    profile_picture_url: data.picture?.data.url,
  };
};

export async function fetchLatestFacebookGraphMedia(
  accessToken: string,
  platformAccountId: string, // IG User ID oder FB Page ID
  env: Bindings,
  limit?: number
): Promise<{ posts: GraphApiMedia[]; error?: any } | null> {
  const contextInfo = `Latest Graph Media for ${platformAccountId}`;
  const collectedPosts: GraphApiMedia[] = [];
  const effectiveLimit = limit ?? Number.MAX_SAFE_INTEGER;
  const fetchLimitPerPage = 50; // Set a constant page size
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url: string | null =
    `https://graph.facebook.com/${constants.META_API_VERSION}/${platformAccountId}/media?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;

  try {
    while (url && collectedPosts.length < effectiveLimit) {
      const remainingLimit = effectiveLimit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(url);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph media page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      );
      const pageJson = await response.json();

      const pageValidation = z
        .object({
          data: z.array(GraphApiMediaSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "GRAPH_MEDIA_PAGE_VALIDATION_ERROR",
          "Invalid graph media page response",
          { errors: pageValidation.error.flatten(), received: pageJson }
        );
        break;
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedPosts.push(...pageData.data);
      } else {
        break;
      }
      if (collectedPosts.length >= effectiveLimit) break;

      url = pageData.paging?.next || null;
      if (url)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }

    console.log(
      `GRAPH_API: Fetched ${collectedPosts.length} graph posts for ${platformAccountId}.`
    );
    return { posts: collectedPosts.slice(0, effectiveLimit) };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch latest graph media for ${platformAccountId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { platformAccountId, error: String(error) }
    );
    return {
      posts: [],
      error: error,
    };
  }
}

export const getPosts = async (
  connection: PlatformConnection,
  { limit, cursor }: { limit?: number; cursor?: string },
  env: Bindings
): Promise<{ posts: Post[]; nextCursor?: string }> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Facebook access token");
  }

  const mediaResult = await fetchLatestFacebookGraphMedia(
    accessToken,
    connection.platformAccountId!,
    env,
    limit ?? connection.postLimit ?? undefined
  );

  if (!mediaResult) {
    throw new Error(`Meta API client failed`);
  }
  if (mediaResult.error) {
    throw mediaResult.error;
  }

  const posts = mediaResult.posts.map((post) =>
    mapFacebookPostData(post, connection)
  );

  return {
    posts: posts,
    nextCursor: undefined, // TODO: Implement cursor logic
  };
};

export const getPost = async (
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<Post | null> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Facebook access token");
  }

  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const url = `https://graph.facebook.com/${constants.META_API_VERSION}/${postId}?fields=${fields}&access_token=${accessToken}`;
  const response = await fetch(url);
  if (!response.ok) {
    return null;
  }
  const postData = await response.json();
  const postValidation = GraphApiMediaSchema.safeParse(postData);

  if (!postValidation.success) {
    return null;
  }

  return mapFacebookPostData(postValidation.data, connection);
};

export function mapFacebookPostData(
  post: GraphApiMedia,
  connection: PlatformConnection
): Post {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "facebook",
    likeCount: post.like_count ?? 0,
    commentsCount: post.comments_count ?? 0,
    caption: post.caption ?? null,
    mediaUrl: post.media_url ?? null,
    mediaType: post.media_type ?? "UNKNOWN",
    permalink: post.permalink ?? null,
    timestamp: post.timestamp ? new Date(post.timestamp) : new Date(),
    lastFetched: new Date(),
    lastWebhookUpdate: null,
    engagement: {},
  };
}
