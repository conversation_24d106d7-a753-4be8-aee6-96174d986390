// src/hono/oauth/meta/select-page.ts
import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { AppContext } from "../../../types";
import { revalidateConnection } from "../../../revalidation-utils";
import { triggerInitialSync } from "../../../sync-utils";

const FacebookPageDetailsSchema = z.object({
  about: z.string().optional(),
  fan_count: z.number().optional(),
  followers_count: z.number().optional(),
  picture: z
    .object({
      data: z.object({
        url: z.string().url(),
      }),
    })
    .optional(),
});

const SelectPageRequestSchema = z.object({
  connectionId: z.string(),
  token: z.string().nullable(),
  userAccessToken: z.string(),
  expiresAt: z.string(),
  selectedPageId: z.string(),
  pages: z.array(z.object({
    id: z.string(),
    name: z.string(),
    access_token: z.string(),
  })),
});

export const selectFacebookPageHandler = async (c: Context<AppContext>) => {
  try {
    const body = await c.req.json();
    const validation = SelectPageRequestSchema.safeParse(body);
    
    if (!validation.success) {
      console.error("Invalid select page request:", validation.error);
      throw new HTTPException(400, { message: "Invalid request data" });
    }
    
    const { connectionId, token, userAccessToken, expiresAt, selectedPageId, pages } = validation.data;
    
    // Find the selected page
    const selectedPage = pages.find(page => page.id === selectedPageId);
    if (!selectedPage) {
      throw new HTTPException(400, { message: "Selected page not found" });
    }
    
    const db = getDbClient(c.env.DB);
    
    // Get connection details
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }
    
    // Get page details
    const pageDetailsResponse = await fetch(
      `https://graph.facebook.com/v19.0/${selectedPage.id}?fields=id,name,about,fan_count,picture.type(large),followers_count&access_token=${selectedPage.access_token}`
    );
    
    if (!pageDetailsResponse.ok) {
      const errorText = await pageDetailsResponse.text();
      console.error("Failed to get page details:", errorText);
      throw new HTTPException(400, { message: "Failed to get page details" });
    }
    
    const pageDetails = await pageDetailsResponse.json();
    const pageDetailsValidation = FacebookPageDetailsSchema.extend({
      id: z.string(),
      name: z.string(),
    }).safeParse(pageDetails);
    
    if (!pageDetailsValidation.success) {
      console.error("Invalid page details response:", pageDetailsValidation.error);
      throw new HTTPException(400, { message: "Invalid page details response" });
    }
    
    const pageInfo = pageDetailsValidation.data;
    
    // Encrypt the page access token
    const encryptedPageToken = await encryptToken(selectedPage.access_token, c.env.ENCRYPTION_KEY);
    
    // Update connection with page information
    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: pageInfo.id,
        platformAccountName: pageInfo.name,
        platformAccountUsername: undefined,
        platformAccountDescription: pageInfo.about,
        platformAccountFollowers: pageInfo.followers_count || pageInfo.fan_count,
        platformAccountFollowing: undefined,
        platformAccountProfilePictureUrl: pageInfo.picture?.data.url,
        platformPostCount: undefined,
        accessTokenEncrypted: encryptedPageToken,
        tokenExpiresAt: new Date(expiresAt),
        isConnected: true,
        isActive: true,
        hasError: false,
        needsReconnect: false,
        lastCheckedAt: new Date(),
        platform: connection.platform,
      })
      .where(eq(schema.platformConnections.id, connectionId));
    
    // Trigger initial post synchronization
    await triggerInitialSync(c.env, connection.id);
    
    // Invalidate all existing generated links for this connection
    try {
      await db
        .update(schema.generatedLinks)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId));
    } catch (invalidateError) {
      console.error("Failed to invalidate existing links:", invalidateError);
      logErrorToAnalytics(
        c.env,
        "META_LINK_INVALIDATION_ERROR",
        "Failed to invalidate existing generated links",
        { connectionId, error: String(invalidateError) }
      );
    }
    
    // Trigger cache revalidation
    if (c.env.REVALIDATION_SECRET) {
      try {
        await revalidateConnection(
          c.env.NEXT_PUBLIC_WEB_URL,
          c.env.REVALIDATION_SECRET,
          connection.projectId,
          connectionId
        );
      } catch (revalidationError) {
        console.error("Cache revalidation failed:", revalidationError);
      }
    }
    
    // Return success response
    return c.json({ 
      success: true, 
      redirectUrl: token 
        ? `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/success`
        : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?success=true`
    });
    
  } catch (error: any) {
    console.error("Facebook page selection error:", error);
    logErrorToAnalytics(
      c.env,
      "FACEBOOK_PAGE_SELECTION_ERROR",
      "Facebook page selection failed",
      { error: String(error) }
    );
    
    if (error instanceof HTTPException) {
      throw error;
    }
    
    throw new HTTPException(500, { message: "Failed to select Facebook page" });
  }
};
