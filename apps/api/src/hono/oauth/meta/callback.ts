// src/hono/oauth/meta/callback.ts
import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken, decryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { AppContext } from "../../../types";
import { revalidateConnection } from "../../../revalidation-utils";
import { triggerInitialSync } from "../../../sync-utils";
import { getPlatformAdapter } from "../../../platforms";

const GraphApiPagesResponseSchema = z.object({
  data: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      access_token: z.string(),
      instagram_business_account: z
        .object({
          id: z.string(),
        })
        .optional(),
    })
  ),
});

const FacebookPageDetailsSchema = z.object({
  about: z.string().optional(),
  fan_count: z.number().optional(),
  followers_count: z.number().optional(),
  picture: z
    .object({
      data: z.object({
        url: z.string().url(),
      }),
    })
    .optional(),
});

export const metaCallbackHandler = async (c: Context<AppContext>) => {
  const code = c.req.query("code");
  const state = c.req.query("state");
  const error = c.req.query("error");

  console.log("Meta OAuth callback received", { code, state, error });

  if (error) {
    console.error("Meta OAuth error:", error);
    return c.redirect(
      `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=${error}`
    );
  }

  if (!code || !state) {
    throw new HTTPException(400, {
      message: "Missing code or state parameter",
    });
  }

  try {
    // Decode state to get connection ID and redirect info
    const stateData = JSON.parse(decodeURIComponent(state));
    const { connectionId, token } = stateData;

    if (!connectionId) {
      throw new HTTPException(400, { message: "Invalid state parameter" });
    }

    const db = getDbClient(c.env.DB);

    // Get connection details
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    const platformAdapter = getPlatformAdapter(connection.platform);

    const { accessToken, expiresAt } = await platformAdapter.exchangeToken(
      code,
      c.env
    );

    // Encrypt the access token for storage
    const encryptedAccessToken = await encryptToken(
      accessToken,
      c.env.ENCRYPTION_KEY
    );

    // Handle different platforms differently
    if (connection.platform === "facebook") {
      // For Facebook, we need to handle page selection
      // Use the access token directly for Facebook pages API

      // Get user's Facebook pages
      const pagesResponse = await fetch(
        `https://graph.facebook.com/v19.0/me/accounts?access_token=${accessToken}`
      );

      if (!pagesResponse.ok) {
        const errorText = await pagesResponse.text();
        console.error("Failed to get Facebook pages:", errorText);
        throw new HTTPException(400, {
          message: "Failed to get Facebook pages",
        });
      }

      const pagesData = await pagesResponse.json();
      const pagesValidation = GraphApiPagesResponseSchema.safeParse(pagesData);

      if (!pagesValidation.success) {
        console.error("Invalid pages response:", pagesValidation.error);
        throw new HTTPException(400, { message: "Invalid pages response" });
      }

      const pages = pagesValidation.data.data;

      if (pages.length === 0) {
        throw new HTTPException(400, {
          message:
            "No Facebook pages found. You need to manage at least one Facebook page to connect.",
        });
      }

      // If multiple pages, redirect to page selection
      if (pages.length > 1) {
        // Store pages data temporarily and redirect to selection page
        const pagesData = {
          connectionId,
          token: token || null,
          userAccessToken: accessToken,
          expiresAt: expiresAt.toISOString(),
          pages: pages.map((page) => ({
            id: page.id,
            name: page.name,
            access_token: page.access_token,
          })),
        };

        // Encode the data for URL
        const encodedData = encodeURIComponent(JSON.stringify(pagesData));

        // Redirect to page selection
        const selectionUrl = token
          ? `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/select-facebook-page?data=${encodedData}`
          : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}/select-facebook-page?data=${encodedData}`;

        return c.redirect(selectionUrl);
      }

      // Use the only available page
      const selectedPage = pages[0];

      // Get page details
      const pageDetailsResponse = await fetch(
        `https://graph.facebook.com/v19.0/${selectedPage.id}?fields=id,name,about,fan_count,picture.type(large),followers_count&access_token=${selectedPage.access_token}`
      );

      if (!pageDetailsResponse.ok) {
        const errorText = await pageDetailsResponse.text();
        console.error("Failed to get page details:", errorText);
        throw new HTTPException(400, { message: "Failed to get page details" });
      }

      const pageDetails = await pageDetailsResponse.json();
      const pageDetailsValidation = FacebookPageDetailsSchema.extend({
        id: z.string(),
        name: z.string(),
      }).safeParse(pageDetails);

      if (!pageDetailsValidation.success) {
        console.error(
          "Invalid page details response:",
          pageDetailsValidation.error
        );
        throw new HTTPException(400, {
          message: "Invalid page details response",
        });
      }

      const pageInfo = pageDetailsValidation.data;

      // Encrypt the page access token (this is the token we'll use for API calls)
      const encryptedPageToken = await encryptToken(
        selectedPage.access_token,
        c.env.ENCRYPTION_KEY
      );

      // Update connection with page information
      await db
        .update(schema.platformConnections)
        .set({
          platformAccountId: pageInfo.id,
          platformAccountName: pageInfo.name,
          platformAccountUsername: undefined, // Facebook pages don't have usernames
          platformAccountDescription: pageInfo.about,
          platformAccountFollowers:
            pageInfo.followers_count || pageInfo.fan_count,
          platformAccountFollowing: undefined, // Not applicable for pages
          platformAccountProfilePictureUrl: pageInfo.picture?.data.url,
          platformPostCount: undefined, // Not available directly
          accessTokenEncrypted: encryptedPageToken, // Use page token, not user token
          tokenExpiresAt: expiresAt,
          isConnected: true,
          isActive: true,
          hasError: false,
          needsReconnect: false,
          lastCheckedAt: new Date(),
          platform: connection.platform, // Keep original platform
        })
        .where(eq(schema.platformConnections.id, connectionId));
    } else {
      // For Instagram Business and other platforms, get user info
      const userInfo = await platformAdapter.getPlatformInformation(
        { ...connection, accessTokenEncrypted: encryptedAccessToken },
        c.env
      );

      if (!userInfo) {
        throw new HTTPException(400, {
          message: "Failed to get user info after token exchange.",
        });
      }

      // Update connection in database
      await db
        .update(schema.platformConnections)
        .set({
          platformAccountId: userInfo.id,
          platformAccountName: userInfo.name ?? userInfo.username,
          platformAccountUsername: userInfo.username,
          platformAccountDescription: userInfo.description,
          platformAccountFollowers: userInfo.followers_count,
          platformAccountFollowing: userInfo.follows_count,
          platformAccountProfilePictureUrl: userInfo.profile_picture_url,
          platformPostCount: userInfo.posts_count,
          accessTokenEncrypted: encryptedAccessToken,
          tokenExpiresAt: expiresAt,
          isConnected: true,
          isActive: true,
          hasError: false,
          needsReconnect: false,
          lastCheckedAt: new Date(),
          platform: connection.platform, // Keep original platform
        })
        .where(eq(schema.platformConnections.id, connectionId));
    }

    // All updates for the instagram business case are already done.

    // Trigger initial post synchronization
    await triggerInitialSync(c.env, connection.id);

    // Invalidate all existing generated links for this connection
    // since the connection is now established and future links should use replace flow
    try {
      const invalidateResult = await db
        .update(schema.generatedLinks)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId));

      console.log(
        "Meta: Invalidated existing generated links:",
        invalidateResult
      );
    } catch (invalidateError) {
      console.error(
        "Meta: Failed to invalidate existing links:",
        invalidateError
      );
      // Don't fail the OAuth flow if link invalidation fails
      logErrorToAnalytics(
        c.env,
        "META_LINK_INVALIDATION_ERROR",
        "Failed to invalidate existing generated links",
        { connectionId, error: String(invalidateError) }
      );
    }

    // Trigger cache revalidation for the connection page
    if (c.env.REVALIDATION_SECRET) {
      try {
        await revalidateConnection(
          c.env.NEXT_PUBLIC_WEB_URL,
          c.env.REVALIDATION_SECRET,
          connection.projectId,
          connectionId
        );
        console.log("Meta: Cache revalidation triggered successfully");
      } catch (revalidationError) {
        console.error("Meta: Cache revalidation failed:", revalidationError);
        // Don't fail the OAuth flow if revalidation fails
      }
    }

    // Redirect based on flow type
    let redirectUrl: string;

    if (token) {
      // Public link flow - redirect to public success page
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/success`;
    } else {
      // Authenticated flow - redirect to dashboard
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?success=true`;
    }

    return c.redirect(redirectUrl);
  } catch (error: any) {
    console.error("Meta OAuth callback error:", error);
    logErrorToAnalytics(
      c.env,
      "META_OAUTH_ERROR",
      "Meta OAuth callback failed",
      { error: String(error) }
    );

    const redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=oauth_failed`;
    return c.redirect(redirectUrl);
  }
};
