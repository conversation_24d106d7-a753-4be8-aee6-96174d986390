import { <PERSON><PERSON> } from "hono";
import { Bindings } from "hono/types";
import { user<PERSON>uth<PERSON>equired } from "../../hono/middleware/user";
import { getAllProjects } from "./project/getAll";
import { createProject, createProjectValidator } from "./project/create";
import { getOneProject } from "./project/get";
import { updateProject, updateProjectValidator } from "./project/update";
import { deleteProject } from "./project/delete";
import { getAllConnections } from "./project/connections/getAll";
import {
  createConnection,
  createConnectionValidator,
} from "./project/connections/create";
import { getOneProjectConnection } from "./project/connections/get";
import { deleteConnection } from "./project/connections/delete";
import {
  updateConnection,
  updateConnectionValidator,
} from "./project/connections/update";
import { testConnection } from "./project/connections/test";
import { syncConnection } from "./project/connections/sync";
import { disconnectConnection } from "./project/connections/disconnect";
import { getAllGeneratedLinks } from "./project/connections/links/getAll";
import {
  createLink,
  createLinkValidator,
} from "./project/connections/links/create";
import { deleteLink } from "./project/connections/links/delete";
import { createFeed, createFeedValidator } from "./project/feeds/create";
import { getAllFeeds } from "./project/feeds/getAll";
import { getOneFeed } from "./project/feeds/get";
import { updateFeed, updateFeedValidator } from "./project/feeds/update";
import { deleteFeed } from "./project/feeds/delete";
import {
  createApiKey,
  createApiKeyValidator,
} from "./project/feeds/apiKey/create";
import { deleteApiKey } from "./project/feeds/apiKey/delete";
import getProjectsCount from "./count/projects";
import { countConnectionsOfAccount } from "./count/connections";
import { getProjectConnectionsCount } from "./count/projectConnections";
import getFeedsCount from "./count/feeds";
import {
  refreshToken,
  refreshTokenValidator,
} from "./project/connections/refresh-token";

const app = new Hono<{ Bindings: Bindings }>();

export const mnageHandler = app
  .use("*", userAuthRequired)
  .get("/projects", getAllProjects)
  .post("/projects", createProjectValidator, createProject)
  .get("/projects/:projectId", getOneProject)
  .post("/projects/:projectId", updateProjectValidator, updateProject)
  .delete("/projects/:projectId", deleteProject)
  .get("/projects/:projectId/connections", getAllConnections)
  .post(
    "/projects/:projectId/connections",
    createConnectionValidator,
    createConnection
  )
  .get(
    "/projects/:projectId/connections/:connectionId",
    getOneProjectConnection
  )
  .delete("/projects/:projectId/connections/:connectionId", deleteConnection)
  .post(
    "/projects/:projectId/connections/:connectionId",
    updateConnectionValidator,
    updateConnection
  )
  .post("/projects/:projectId/connections/:connectionId/test", testConnection)
  .post("/projects/:projectId/connections/:connectionId/sync", syncConnection)
  .post(
    "/projects/:projectId/connections/:connectionId/refreshtoken",
    refreshTokenValidator,
    refreshToken
  )
  .post(
    "/projects/:projectId/connections/:connectionId/disconnect",
    disconnectConnection
  )
  .get(
    "/projects/:projectId/connections/:connectionId/links",
    getAllGeneratedLinks
  )
  .post(
    "/projects/:projectId/connections/:connectionId/links",
    createLinkValidator,
    createLink
  )
  .delete(
    "/projects/:projectId/connections/:connectionId/links/:linkId",
    deleteLink
  )
  .post("/projects/:projectId/feeds", createFeedValidator, createFeed)
  .get("/projects/:projectId/feeds", getAllFeeds)
  .get("/projects/:projectId/feeds/:feedId", getOneFeed)
  .post("/projects/:projectId/feeds/:feedId", updateFeedValidator, updateFeed)
  .delete("/projects/:projectId/feeds/:feedId", deleteFeed)
  .post(
    "/projects/:projectId/feeds/:feedId/apiKeys",
    createApiKeyValidator,
    createApiKey
  )
  .delete("/projects/:projectId/feeds/:feedId/apiKeys/:apiKey", deleteApiKey)
  .get("/count/projects", getProjectsCount)
  .get("/count/connections", countConnectionsOfAccount)
  .get("/count/projects/:projectId/connections", getProjectConnectionsCount)
  .get("/count/projects/:projectId/feeds", getFeedsCount);
