import { Context, InferResponseType } from "hono";
import { AppContext } from "../../../../types";
import { BlankInput, ParsedFormValue } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq, count } from "drizzle-orm";
import { getConnectionStatus } from "../../../../utils/connectionStatus";

export type getOneProjectConnectionReturnType = {
  id: string;
  name: string;
  platform: string;
  platformAccountName: string | null;
  platformAccountUsername: string | null;
  platformAccountDescription: string | null;
  platformAccountFollowers: number | null;
  platformAccountFollowing: number | null;
  platformAccountProfilePictureUrl: string | null;
  platformPostCount: number | null;
  isActive: boolean;
  hasError: boolean;
  isConnected: boolean;
  tokenExpiresAt: Date | null;
  createdAt: Date | null;
  lastPolledAt: Date | null;
  subscriberCount: number | null;
  status: "active" | "inactive" | "auth_needed" | "expired" | "error";
  isSyncing: boolean;
  lastSyncedAt: Date | null;
  postCount: number;
};

export const getOneProjectConnection = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId",
    BlankInput
  >
) => {
  const requestedProjectId = c.req.param("projectId");
  if (!requestedProjectId)
    throw new HTTPException(403, {
      message: "Project ID required",
    });

  const requestedConnectionId = c.req.param("connectionId");
  if (!requestedConnectionId)
    throw new HTTPException(403, {
      message: "Connection ID required",
    });

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);
  try {
    const connections = await db
      .select({
        platformConnection: schema.platformConnections,
        postCount: count(schema.posts.mediaId),
      })
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .leftJoin(
        schema.posts,
        eq(schema.platformConnections.id, schema.posts.platformConnectionId)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .groupBy(schema.platformConnections.id)
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    return c.json({
      ...connections[0].platformConnection,
      postCount: connections[0].postCount,
      status: getConnectionStatus({
        isActive: connections[0].platformConnection.isActive,
        hasError: connections[0].platformConnection.hasError,
        isConnected: connections[0].platformConnection.isConnected,
        tokenExpiresAt: connections[0].platformConnection.tokenExpiresAt,
      }),
    } satisfies getOneProjectConnectionReturnType);
  } catch (e) {
    console.error(
      `Failed to fetch connection ${requestedConnectionId} for project ${requestedProjectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_CONNECTION_FETCH_ERROR",
      `Failed to fetch connection ${requestedConnectionId} for project ${requestedProjectId} for org ${organizationId}`,
      {
        projectId: requestedProjectId,
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch projects connection",
    });
  }
};
