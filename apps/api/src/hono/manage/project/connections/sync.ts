import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";
import { getPlatformAdapter } from "../../../../platforms";
import { decryptToken } from "../../../../token-utils";

export const syncConnection = async (c: Context<AppContext>) => {
  console.log(
    "LOG: syncConnection called with params:",
    c.req.param("projectId"),
    c.req.param("connectionId")
  );
  console.log(
    "ENCRYPTION_KEY available in syncConnection:",
    c.env.ENCRYPTION_KEY ? "Yes" : "No"
  );
  const requestedProjectId = c.req.param("projectId");
  const requestedConnectionId = c.req.param("connectionId");
  const organizationId = c.var.organizationId;

  if (!organizationId) {
    throw new HTTPException(401, {
      message: "Organization ID not found",
    });
  }

  if (!requestedProjectId) {
    throw new HTTPException(400, {
      message: "Project ID is required",
    });
  }

  if (!requestedConnectionId) {
    throw new HTTPException(400, {
      message: "Connection ID is required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    const connections = await db
      .select()
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    const connection = connections[0].platform_connections;

    if (!connection.accessTokenEncrypted) {
      return c.json({
        success: false,
        error: "No access token found. Please reconnect.",
      });
    }

    const platformAdapter = getPlatformAdapter(connection.platform);

    const accessToken = await decryptToken(
      connection.accessTokenEncrypted,
      c.env.ENCRYPTION_KEY
    );

    if (!accessToken) {
      return c.json({
        success: false,
        error: "Failed to decrypt access token. Please reconnect.",
      });
    }

    console.log("WE ARE GETTING THE PLATFORM ADAPTER", connection.platform);

    const [{ posts }, platformInfo] = await Promise.all([
      platformAdapter.getPosts(connection, {}, c.env),
      platformAdapter.getPlatformInformation(connection, c.env),
    ]);

    await db
      .update(schema.platformConnections)
      .set({
        lastSyncedAt: new Date(),
        platformAccountName: platformInfo?.name,
        platformAccountProfilePictureUrl: platformInfo?.profile_picture_url,
      })
      .where(eq(schema.platformConnections.id, requestedConnectionId));

    return c.json({ success: true, posts });
  } catch (e) {
    console.error(
      `Failed to sync connection ${requestedConnectionId} for project ${requestedProjectId} in org ${organizationId}:`,
      e
    );

    logErrorToAnalytics(
      c.env,
      "CONNECTION_SYNC_ERROR",
      "Failed to sync connection",
      {
        connectionId: requestedConnectionId,
        projectId: requestedProjectId,
        organizationId,
        error: String(e),
      }
    );

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to sync connection",
    });
  }
};
